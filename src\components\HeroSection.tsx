
import { useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { useLanguage } from "@/context/LanguageContext";
import Typed from "typed.js";

const HeroSection = () => {
  const { t, language } = useLanguage();
  const typedElementRef = useRef<HTMLSpanElement>(null);
  const typedInstanceRef = useRef<Typed | null>(null);

  useEffect(() => {
    if (typedElementRef.current) {
      // Destroy previous instance if it exists
      if (typedInstanceRef.current) {
        typedInstanceRef.current.destroy();
      }
      
      // Strings based on current language
      let strings;
      
      switch(language) {
        case "english":
          strings = [
            "Chief Business Officer.",
            "Chief Operating Officer.",
            "Founder & Entrepreneur.",
            "Strategic Leader.",
            "Academic & Industry Professional."
          ];
          break;
        case "hindi":
          strings = [
            "मुख्य व्यापार अधिकारी.",
            "मुख्य परिचालन अधिकारी.",
            "संस्थापक और उद्यमी.",
            "रणनीतिक नेता.",
            "शैक्षिक और उद्योग पेशेवर."
          ];
          break;
        case "spanish":
          strings = [
            "Director de Negocios.",
            "Director de Operaciones.",
            "Fundador y Emprendedor.",
            "Líder Estratégico.",
            "Profesional Académico e Industrial."
          ];
          break;
        default:
          strings = [
            "Chief Business Officer.",
            "Chief Operating Officer.",
            "Founder & Entrepreneur.",
            "Strategic Leader.",
            "Academic & Industry Professional."
          ];
      }
      
      // Create new instance
      typedInstanceRef.current = new Typed(typedElementRef.current, {
        strings,
        typeSpeed: 50,
        backSpeed: 30,
        backDelay: 1500,
        startDelay: 500,
        loop: true,
      });
    }

    return () => {
      if (typedInstanceRef.current) {
        typedInstanceRef.current.destroy();
      }
    };
  }, [language]);

  const scrollToContact = () => {
    const contactSection = document.getElementById("contact");
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section id="home" className="min-h-screen flex items-center relative overflow-hidden">
      {/* 3D Background Canvas */}
      <div id="hero-canvas" className="absolute top-0 left-0 w-full h-full -z-10"></div>
      
      {/* Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 z-10">
        <div className="max-w-3xl">
          <p className="text-primary font-medium mb-4 opacity-0 animate-fade-in" style={{animationDelay: '300ms'}}>
            {t("hero.greeting")}
          </p>
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-4 opacity-0 animate-fade-in" style={{animationDelay: '500ms'}}>
            {t("hero.name")}
          </h1>
          <h2 className="text-3xl sm:text-4xl md:text-5xl text-muted-foreground font-bold mb-6 opacity-0 animate-fade-in" style={{animationDelay: '700ms'}}>
            <span ref={typedElementRef}></span>
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl opacity-0 animate-fade-in" style={{animationDelay: '900ms'}}>
            {t("hero.description")}
          </p>
          <div className="opacity-0 animate-fade-in" style={{animationDelay: '1100ms'}}>
            <Button 
              size="lg"
              onClick={scrollToContact}
              className="group"
            >
              {t("hero.cta")}
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </div>
      </div>
      
      {/* Scroll indicator */}
      <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center opacity-0 animate-fade-in" style={{animationDelay: '1500ms'}}>
        <span className="text-sm text-muted-foreground mb-2">{t("hero.scroll") || "Scroll"}</span>
        <div className="w-5 h-10 rounded-full border-2 border-primary flex justify-center p-1">
          <div className="w-1 h-2 bg-primary rounded-full animate-bounce"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
