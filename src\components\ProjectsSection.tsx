
import { useEffect, useRef } from "react";
import { useLanguage } from "@/context/LanguageContext";
import { Button } from "@/components/ui/button";
import { Link, Briefcase } from "lucide-react";
import { Card, CardContent, CardFooter } from "./ui/card";

const ProjectsSection = () => {
  const { t } = useLanguage();
  const sectionRef = useRef<HTMLElement>(null);
  
  const projects = [
    {
      titleKey: "projects.offshore.title",
      descriptionKey: "projects.offshore.description",
      icon: <Briefcase className="h-8 w-8" />,
      color: "from-blue-500 to-purple-500",
    },
    {
      titleKey: "projects.policy.title",
      descriptionKey: "projects.policy.description",
      icon: <Briefcase className="h-8 w-8" />,
      color: "from-green-500 to-blue-500",
    },
    {
      titleKey: "projects.msme.title",
      descriptionKey: "projects.msme.description",
      icon: <Briefcase className="h-8 w-8" />,
      color: "from-orange-500 to-red-500",
    }
  ];

  const ProjectCard = ({ project }: { project: { titleKey: string; descriptionKey: string; icon: React.ReactNode; color: string } }) => {
    return (
      <Card className="project-card group">
        <div className={`h-2 bg-gradient-to-r ${project.color}`}></div>
        <CardContent className="pt-6">
          <div className={`w-14 h-14 rounded-full bg-gradient-to-br ${project.color} flex items-center justify-center mb-5 text-white shadow-md group-hover:scale-110 transition-transform duration-300`}>
            {project.icon}
          </div>
          <h3 className="text-xl font-bold mb-3">{t(project.titleKey)}</h3>
          <p className="text-muted-foreground">{t(project.descriptionKey)}</p>
        </CardContent>
        <CardFooter className="pt-0">
          <Button variant="ghost" size="sm" className="ml-auto group-hover:text-primary transition-colors">
            {t("projects.learnMore")}
            <Link size={16} className="ml-1.5 group-hover:translate-x-1 transition-transform" />
          </Button>
        </CardFooter>
      </Card>
    );
  };
  
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "0px",
      threshold: 0.1,
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const elements = sectionRef.current?.querySelectorAll(".reveal");
          elements?.forEach((el, index) => {
            setTimeout(() => {
              el.classList.add("animate-fade-in");
            }, index * 150); // Standardized timing
          });
          observer.unobserve(entry.target);
        }
      });
    }, options);

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section id="projects" ref={sectionRef} className="py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-16">
          <h2 className="section-title reveal opacity-0 mb-0">
            {t("projects.title")}
          </h2>
          <Button variant="outline" className="reveal opacity-0">
            {t("projects.viewAll")}
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <div key={index} className="reveal opacity-0 h-full" style={{ animationDelay: `${index * 150}ms` }}>
              <ProjectCard project={project} />
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center reveal opacity-0">
          <p className="text-lg max-w-3xl mx-auto">
            {t("projects.summary")}
          </p>
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;
