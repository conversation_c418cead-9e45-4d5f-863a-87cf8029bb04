
import { useEffect } from 'react';

const Background3D = () => {
  useEffect(() => {
    // Check if Three.js script is already loaded
    const isScriptLoaded = () => {
      return typeof (window as any).THREE !== 'undefined';
    };

    // Load Three.js script if not already loaded
    if (!isScriptLoaded()) {
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js';
      script.async = true;
      script.onload = initThreeJS;
      document.body.appendChild(script);
    } else {
      initThreeJS();
    }

    function initThreeJS() {
      const THREE = (window as any).THREE;
      
      // Get the container element
      const container = document.getElementById('hero-canvas');
      if (!container) return;
      
      // Scene, camera, and renderer setup
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(
        75,
        window.innerWidth / window.innerHeight,
        0.1,
        1000
      );
      
      const renderer = new THREE.WebGLRenderer({ alpha: true });
      renderer.setSize(container.clientWidth, container.clientHeight);
      renderer.setClearColor(0x000000, 0); // Transparent background
      container.appendChild(renderer.domElement);
      
      // Create particles
      const particlesGeometry = new THREE.BufferGeometry();
      const particlesCount = 1500;
      
      const posArray = new Float32Array(particlesCount * 3);
      
      for (let i = 0; i < particlesCount * 3; i++) {
        // Random positions for x, y, z
        posArray[i] = (Math.random() - 0.5) * 10;
      }
      
      particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
      
      // Material
      const particlesMaterial = new THREE.PointsMaterial({
        size: 0.02,
        color: isDarkMode() ? 0xffffff : 0x0a192f,
        transparent: true,
        opacity: 0.5,
      });
      
      // Mesh
      const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
      scene.add(particlesMesh);
      
      // Position camera
      camera.position.z = 5;
      
      // Mouse movement tracker
      const mouse = {
        x: 0,
        y: 0,
      };
      
      document.addEventListener('mousemove', (event) => {
        mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
      });
      
      // Animation loop
      const animate = () => {
        requestAnimationFrame(animate);
        
        // Rotate particles slowly
        particlesMesh.rotation.x += 0.0003;
        particlesMesh.rotation.y += 0.0003;
        
        // Respond to mouse movement
        if (mouse.x && mouse.y) {
          particlesMesh.rotation.x += mouse.y * 0.0001;
          particlesMesh.rotation.y += mouse.x * 0.0001;
        }
        
        renderer.render(scene, camera);
      };
      
      // Handle window resize
      const handleResize = () => {
        camera.aspect = container.clientWidth / container.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(container.clientWidth, container.clientHeight);
      };
      
      window.addEventListener('resize', handleResize);
      
      // Handle theme changes to update particle color
      const themeObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const isDark = isDarkMode();
            particlesMaterial.color.set(isDark ? 0xffffff : 0x0a192f);
          }
        });
      });
      
      themeObserver.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      });
      
      // Start animation
      animate();
      
      // Cleanup on component unmount
      return () => {
        if (container.contains(renderer.domElement)) {
          container.removeChild(renderer.domElement);
        }
        window.removeEventListener('resize', handleResize);
        themeObserver.disconnect();
        
        // Dispose resources
        particlesGeometry.dispose();
        particlesMaterial.dispose();
        renderer.dispose();
      };
    }
    
    // Check if dark mode is active
    function isDarkMode() {
      return document.documentElement.classList.contains('dark');
    }
    
    // Clean up
    return () => {
      const canvas = document.getElementById('hero-canvas')?.firstChild as HTMLElement;
      if (canvas) {
        canvas.remove();
      }
    };
  }, []);

  return null;
};

export default Background3D;
