
import { useEffect, useRef } from "react";
import { useLanguage } from "@/context/LanguageContext";

interface Skill {
  name: string;
  progress: number;
  category: string;
  nameKey: string;
  categoryKey: string;
}

const SkillsSection = () => {
  const { t } = useLanguage();
  const sectionRef = useRef<HTMLElement>(null);
  const progressBarsRef = useRef<HTMLDivElement[]>([]);
  
  const skills: Skill[] = [
    { name: t("skills.leadership.strategic") || "Strategic Leadership", progress: 95, category: t("skills.category.leadership") || "Leadership", nameKey: "skills.leadership.strategic", categoryKey: "skills.category.leadership" },
    { name: t("skills.leadership.offshore") || "Offshore Operations", progress: 90, category: t("skills.category.leadership") || "Leadership", nameKey: "skills.leadership.offshore", categoryKey: "skills.category.leadership" },
    { name: t("skills.leadership.crossfunctional") || "Team Management", progress: 88, category: t("skills.category.leadership") || "Leadership", nameKey: "skills.leadership.crossfunctional", categoryKey: "skills.category.leadership" },
    { name: t("skills.operations.policy") || "Policy Development", progress: 90, category: t("skills.category.operations") || "Operations", nameKey: "skills.operations.policy", categoryKey: "skills.category.operations" },
    { name: t("skills.operations.workflow") || "Process Optimization", progress: 92, category: t("skills.category.operations") || "Operations", nameKey: "skills.operations.workflow", categoryKey: "skills.category.operations" },
    { name: t("skills.operations.tech") || "Technology Implementation", progress: 85, category: t("skills.category.operations") || "Operations", nameKey: "skills.operations.tech", categoryKey: "skills.category.operations" },
    { name: t("skills.business.revenue") || "Revenue Growth", progress: 90, category: t("skills.category.business") || "Business Development", nameKey: "skills.business.revenue", categoryKey: "skills.category.business" },
    { name: t("skills.consulting.msme") || "MSME Consulting", progress: 85, category: t("skills.category.business") || "Business Development", nameKey: "skills.consulting.msme", categoryKey: "skills.category.business" },
  ];

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "0px",
      threshold: 0.1,
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const elements = sectionRef.current?.querySelectorAll(".reveal");
          elements?.forEach((el, index) => {
            setTimeout(() => {
              el.classList.add("animate-fade-in");
            }, index * 100);
          });

          // Animate progress bars when in view
          progressBarsRef.current.forEach((bar, index) => {
            if (bar) {
              setTimeout(() => {
                bar.style.width = `${skills[index].progress}%`;
              }, 300 + index * 100);
            }
          });
          
          observer.unobserve(entry.target);
        }
      });
    }, options);

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  // Get unique categories
  const uniqueCategories = [...new Set(skills.map(skill => skill.category))];

  return (
    <section id="skills" ref={sectionRef} className="py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="section-title reveal opacity-0">
          {t("skills.title")}
        </h2>
        
        <div className="mt-12 grid grid-cols-1 lg:grid-cols-3 gap-8">
          {uniqueCategories.map((category, categoryIndex) => (
            <div key={category} className="reveal opacity-0" style={{animationDelay: `${categoryIndex * 200}ms`}}>
              <h3 className="text-xl font-bold mb-6 text-center">{category}</h3>
              <div className="space-y-4">
                {skills
                  .filter(skill => skill.category === category)
                  .map((skill, index) => {
                    const globalIndex = skills.findIndex(s => s.name === skill.name);
                    return (
                      <div key={skill.name} className="space-y-2">
                        <div className="flex justify-between">
                          <span className="font-medium text-sm">{skill.name}</span>
                          <span className="text-muted-foreground text-sm">{skill.progress}%</span>
                        </div>
                        <div className="skill-bar">
                          <div 
                            ref={el => {
                              if (el) progressBarsRef.current[globalIndex] = el;
                            }}
                            className="skill-progress"
                          ></div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SkillsSection;
