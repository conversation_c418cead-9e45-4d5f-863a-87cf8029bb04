import { useEffect, useRef } from "react";
import { useLanguage } from "@/context/LanguageContext";
const AboutSection = () => {
  const {
    t
  } = useLanguage();
  const sectionRef = useRef<HTMLElement>(null);
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "0px",
      threshold: 0.1
    };
    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const elements = sectionRef.current?.querySelectorAll(".reveal");
          elements?.forEach((el, index) => {
            setTimeout(() => {
              el.classList.add("animate-fade-in");
            }, index * 150); // Standardized animation timing
          });
          observer.unobserve(entry.target);
        }
      });
    }, options);
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);
  return <section id="about" ref={sectionRef} className="py-24 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="section-title reveal opacity-0 mb-16 text-3xl md:text-4xl font-bold">
          {t("about.title")}
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <p className="text-lg leading-relaxed reveal opacity-0 text-foreground/90">
              {t("about.p1")}
            </p>
            <p className="text-lg leading-relaxed reveal opacity-0 text-foreground/90">
              {t("about.p2")}
            </p>
            <p className="text-lg leading-relaxed reveal opacity-0 text-foreground/90">
              {t("about.p3")}
            </p>
          </div>
          
          <div className="relative reveal opacity-0 max-w-md mx-auto md:mx-0 md:ml-auto">
            <div className="image-container relative z-10">
              <div className="aspect-square bg-muted relative rounded-xl overflow-hidden border-2 border-primary/80 shadow-xl transition-all duration-500 hover:shadow-primary/20">
                {/* Profile image with improved styling */}
                <img src="/lovable-uploads/9f3784c4-dfbf-42a5-b921-5a388d683546.png" alt="Shreyas Nambiar" className="w-full h-full rounded-lg object-contain" />
                
                {/* Decorative elements with refined styling */}
                <div className="absolute -bottom-3 -right-3 w-full h-full border-2 border-primary/40 rounded-xl z-[-1] bg-background/5"></div>
                <div className="absolute -bottom-6 -right-6 w-full h-full border-2 border-primary/20 rounded-xl z-[-2] bg-background/5"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>;
};
export default AboutSection;