
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme - slightly refined color scheme */
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme - refined for better contrast */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 100% 50%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Improved typographic scale */
  h1, h2, h3, h4, h5, h6 {
    @apply font-montserrat font-bold tracking-tight text-foreground;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl leading-tight;
  }

  h2 {
    @apply text-3xl md:text-4xl leading-tight;
  }

  h3 {
    @apply text-2xl md:text-3xl leading-tight;
  }

  h4 {
    @apply text-xl md:text-2xl leading-tight;
  }

  p {
    @apply text-base md:text-lg font-normal leading-relaxed text-foreground/90;
  }
}

@layer components {
  /* Improved section styling with consistent spacing */
  .section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 py-20 md:py-28;
  }
  
  .section-title {
    @apply text-3xl md:text-4xl font-bold relative inline-block mb-12;
  }
  
  .section-title::after {
    content: '';
    @apply absolute bottom-0 left-0 w-24 h-1 bg-primary rounded-full transition-all duration-500;
  }

  .section-title:hover::after {
    @apply w-full;
  }

  /* Refined skill bar styling */
  .skill-bar {
    @apply h-3 bg-secondary rounded-full overflow-hidden relative shadow-inner;
  }

  .skill-progress {
    @apply h-full bg-primary absolute left-0 top-0 transition-all duration-1000 ease-out rounded-full;
    width: 0%;
  }

  /* Enhanced navigation styling */
  .nav-link {
    @apply relative px-3 py-2 text-foreground transition-all duration-300 hover:text-primary;
  }

  .nav-link::after {
    content: '';
    @apply absolute left-0 bottom-0 w-0 h-0.5 bg-primary transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  .nav-link.active {
    @apply text-primary font-medium;
  }

  .nav-link.active::after {
    @apply w-full;
  }
  
  /* Improved social icon styling */
  .social-icon {
    @apply w-10 h-10 rounded-full flex items-center justify-center text-foreground hover:text-primary hover:bg-background/80 transition-all duration-300 border border-muted hover:border-primary hover:scale-110;
  }
  
  /* Enhanced project card styling */
  .project-card {
    @apply relative bg-card border border-border rounded-lg shadow-sm overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary/30;
  }

  /* Card hover effect */
  .project-card:hover .card-overlay {
    @apply opacity-100;
  }
}

/* Improved scroll indicator */
.scroll-indicator {
  @apply fixed top-0 left-0 h-1.5 bg-primary z-50;
  width: 0%;
}

/* Enhanced custom cursor */
.custom-cursor {
  @apply fixed w-5 h-5 rounded-full pointer-events-none z-50 -translate-x-1/2 -translate-y-1/2 bg-white/70 backdrop-blur-sm;
  transition: transform 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28), 
              background-color 0.3s ease-out,
              width 0.2s ease-out,
              height 0.2s ease-out;
  mix-blend-mode: difference;
}

.cursor-hover {
  @apply w-8 h-8 bg-white/90;
  transform: translate(-50%, -50%) scale(1.2);
}

/* Improved typewriter effect */
.typewriter {
  @apply overflow-hidden whitespace-nowrap border-r-4 border-primary;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: hsl(var(--primary)) }
}

/* Standardized animations */
.animate-fade-in {
  animation: fadeIn 0.7s ease-out forwards;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced glass effect */
.glass-effect {
  @apply backdrop-blur-md bg-white/10 border border-white/20 shadow-lg;
}

/* Image container with hover effect */
.image-container {
  @apply transition-all duration-500;
}

.image-container:hover {
  @apply -translate-y-2;
}
