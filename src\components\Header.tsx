
import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import ThemeToggle from "./ThemeToggle";
import LanguageSwitcher from "./LanguageSwitcher";
import { useLanguage } from "@/context/LanguageContext";

const Header = () => {
  const { t } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [activeSection, setActiveSection] = useState("home");

  const navLinks = [
    { id: "home", label: t("nav.home") },
    { id: "about", label: t("nav.about") },
    { id: "experience", label: t("nav.experience") },
    { id: "projects", label: t("nav.projects") },
    { id: "skills", label: t("nav.skills") },
    { id: "contact", label: t("nav.contact") },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY);
      
      // Improved section detection with better threshold
      const sections = document.querySelectorAll("section[id]");
      let currentSection = "home";
      const offset = 150; // Adjusted offset for better section detection
      
      sections.forEach((section) => {
        // Cast section to HTMLElement to access offsetTop and offsetHeight
        const htmlSection = section as HTMLElement;
        const sectionTop = htmlSection.offsetTop - offset;
        const sectionHeight = htmlSection.offsetHeight;
        
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
          currentSection = section.getAttribute("id") || "home";
        }
      });
      
      setActiveSection(currentSection);
    };
    
    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Initial check
    
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const handleNavClick = (id: string) => {
    setIsMenuOpen(false);
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <>
      {/* Improved scroll indicator with smoother transition */}
      <div 
        className="scroll-indicator" 
        style={{ 
          width: `${(scrollPosition / (document.documentElement.scrollHeight - window.innerHeight)) * 100}%`,
          transition: "width 0.1s ease-out"
        }}
      />
      
      <header className={`fixed top-0 left-0 w-full z-40 transition-all duration-300 ${
        scrollPosition > 50 
          ? "bg-background/90 backdrop-blur-md shadow-sm py-2" 
          : "bg-transparent py-4"
      }`}>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <a 
            href="#home" 
            onClick={() => handleNavClick("home")}
            className="text-xl font-bold text-primary hover:text-primary/80 transition-colors"
          >
            SN
          </a>
          
          {/* Desktop Navigation with improved active states */}
          <nav className="hidden md:flex items-center space-x-1">
            {navLinks.map((link, index) => (
              <a
                key={link.id}
                href={`#${link.id}`}
                onClick={(e) => {
                  e.preventDefault();
                  handleNavClick(link.id);
                }}
                className={`nav-link ${activeSection === link.id ? "active" : ""}`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                {link.label}
              </a>
            ))}
            
            <div className="ml-4 flex items-center space-x-2">
              <ThemeToggle />
              <LanguageSwitcher />
              <Button 
                size="sm" 
                className="ml-2 font-medium"
                onClick={() => window.open("/resume.pdf", "_blank")}
              >
                Resume
              </Button>
            </div>
          </nav>
          
          {/* Mobile Menu Button */}
          <div className="flex items-center space-x-2 md:hidden">
            <ThemeToggle />
            <LanguageSwitcher />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
              className="transition-transform"
            >
              {isMenuOpen ? <X /> : <Menu />}
            </Button>
          </div>
        </div>
        
        {/* Improved Mobile Navigation with smoother transitions */}
        <div 
          className={`md:hidden fixed inset-0 z-30 bg-background/95 backdrop-blur-md transition-transform duration-300 ease-in-out ${
            isMenuOpen ? "transform translate-x-0" : "transform translate-x-full"
          }`}
        >
          <div className="flex flex-col items-center justify-center h-full space-y-6">
            {navLinks.map((link, index) => (
              <a
                key={link.id}
                href={`#${link.id}`}
                onClick={(e) => {
                  e.preventDefault();
                  handleNavClick(link.id);
                }}
                className={`text-xl py-3 font-medium transition-colors ${
                  activeSection === link.id 
                    ? "text-primary" 
                    : "text-foreground hover:text-primary"
                }`}
                style={{ transitionDelay: `${index * 50}ms` }}
              >
                {link.label}
              </a>
            ))}
            <Button 
              className="mt-6"
              onClick={() => {
                window.open("/resume.pdf", "_blank");
                setIsMenuOpen(false);
              }}
            >
              Resume
            </Button>
          </div>
        </div>
      </header>
    </>
  );
};

export default Header;
