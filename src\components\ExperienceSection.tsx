
import { useEffect, useRef, useState } from "react";
import { useLanguage } from "@/context/LanguageContext";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";

interface Experience {
  titleKey: string;
  companyKey: string;
  periodKey: string;
  descriptionKey: string;
}

const ExperienceSection = () => {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  const experiences: Experience[] = [
    {
      titleKey: "experience.universal.title",
      companyKey: "experience.universal.company",
      periodKey: "experience.universal.period",
      descriptionKey: "experience.universal.desc",
    },
    {
      titleKey: "experience.univitt.title",
      companyKey: "experience.univitt.company",
      periodKey: "experience.univitt.period",
      descriptionKey: "experience.univitt.desc",
    },
    {
      titleKey: "experience.fams.title",
      companyKey: "experience.fams.company",
      periodKey: "experience.fams.period",
      descriptionKey: "experience.fams.desc",
    },
  ];

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "0px",
      threshold: 0.1,
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const elements = sectionRef.current?.querySelectorAll(".reveal");
          elements?.forEach((el, index) => {
            setTimeout(() => {
              el.classList.add("animate-fade-in");
            }, index * 200);
          });
          observer.unobserve(entry.target);
        }
      });
    }, options);

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section id="experience" ref={sectionRef} className="py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="section-title reveal opacity-0">
          {t("experience.title")}
        </h2>
        
        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company tabs - vertical on desktop, horizontal on mobile */}
          <div className="flex md:flex-col overflow-x-auto md:overflow-visible reveal opacity-0">
            {experiences.map((exp, index) => (
              <Button
                key={index}
                variant={activeTab === index ? "default" : "outline"}
                className={`justify-start mb-2 mr-2 md:mr-0 whitespace-nowrap ${
                  activeTab === index ? "border-l-4 border-primary" : ""
                }`}
                onClick={() => setActiveTab(index)}
              >
                {t(exp.companyKey)}
              </Button>
            ))}
          </div>
          
          {/* Experience details */}
          <div className="md:col-span-3">
            <div className="reveal opacity-0">
              <h3 className="text-xl font-bold mb-1">
                {t(experiences[activeTab].titleKey)}
              </h3>
              <h4 className="text-lg text-primary mb-2">
                {t(experiences[activeTab].companyKey)}
              </h4>
              <p className="flex items-center text-muted-foreground mb-4">
                <Calendar className="h-4 w-4 mr-2" />
                {t(experiences[activeTab].periodKey)}
              </p>
              <p className="text-lg">
                {t(experiences[activeTab].descriptionKey)}
              </p>
            </div>
          </div>
        </div>
        
        <div className="mt-16 reveal opacity-0">
          <h3 className="text-xl font-bold mb-4">{t("experience.academic.title")}</h3>
          <p className="text-lg">
            {t("experience.academic.description")}
          </p>
        </div>
      </div>
    </section>
  );
};

export default ExperienceSection;
