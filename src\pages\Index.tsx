
import { useEffect } from "react";
import Header from "@/components/Header";
import HeroSection from "@/components/HeroSection";
import AboutSection from "@/components/AboutSection";
import ExperienceSection from "@/components/ExperienceSection";
import ProjectsSection from "@/components/ProjectsSection";
import SkillsSection from "@/components/SkillsSection";
import ContactSection from "@/components/ContactSection";
import Footer from "@/components/Footer";
import Background3D from "@/components/Background3D";

const Index = () => {
  useEffect(() => {
    // Improved custom cursor with consistent styling
    const createCursor = () => {
      const cursorEl = document.createElement('div');
      cursorEl.className = 'custom-cursor';
      document.body.appendChild(cursorEl);
      
      document.addEventListener('mousemove', (e) => {
        // Use requestAnimationFrame for smoother cursor movement
        requestAnimationFrame(() => {
          cursorEl.style.left = `${e.clientX}px`;
          cursorEl.style.top = `${e.clientY}px`;
        });
      });
      
      // Enhanced hover effect on interactive elements
      const interactiveElements = document.querySelectorAll(
        'a, button, input, textarea, select, .project-card, .nav-link, .social-icon'
      );
      
      interactiveElements.forEach((el) => {
        el.addEventListener('mouseenter', () => {
          cursorEl.classList.add('cursor-hover');
        });
        
        el.addEventListener('mouseleave', () => {
          cursorEl.classList.remove('cursor-hover');
        });
      });
    };
    
    // Only create custom cursor on desktop
    if (window.innerWidth > 768) {
      createCursor();
    }
    
    // Update title with type effect
    let titleIndex = 0;
    const titles = [
      "Shreyas Nambiar | Portfolio",
      "Shreyas Nambiar | Chief Business Officer",
      "Shreyas Nambiar | Chief Operating Officer",
      "Shreyas Nambiar | Strategic Leader"
    ];
    
    const updateTitle = () => {
      document.title = titles[titleIndex];
      titleIndex = (titleIndex + 1) % titles.length;
    };
    
    const titleInterval = setInterval(updateTitle, 3000);
    
    return () => {
      clearInterval(titleInterval);
      
      // Remove custom cursor
      const cursor = document.querySelector('.custom-cursor');
      if (cursor) {
        document.body.removeChild(cursor);
      }
    };
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        <HeroSection />
        <Background3D />
        <AboutSection />
        <ExperienceSection />
        <ProjectsSection />
        <SkillsSection />
        <ContactSection />
      </main>
      <Footer />
    </div>
  );
};

export default Index;
