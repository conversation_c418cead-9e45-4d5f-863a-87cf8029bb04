
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Languages } from "lucide-react";
import { useLanguage } from "@/context/LanguageContext";

const LanguageSwitcher = () => {
  const { language, setLanguage } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);

  const toggleLanguage = (newLang: string) => {
    setLanguage(newLang);
    setIsOpen(false);
  };

  const languageOptions = [
    { code: "english", name: "English" },
    { code: "spanish", name: "Español" },
    { code: "hindi", name: "हिन्दी" },
    { code: "tamil", name: "தமிழ்" },
    { code: "bengali", name: "বাংলা" },
    { code: "telugu", name: "తెలుగు" },
    { code: "marathi", name: "मराठी" },
    { code: "french", name: "Français" },
    { code: "german", name: "<PERSON><PERSON><PERSON>" },
    { code: "chinese", name: "中文" },
    { code: "japanese", name: "日本語" },
    { code: "arabic", name: "العربية" }
  ];

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className="w-10 h-10 rounded-full border border-border"
        aria-label="Change language"
      >
        <Languages className="h-5 w-5" />
      </Button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 bg-card border border-border rounded-lg shadow-md p-2 z-20 max-h-80 overflow-y-auto">
          {languageOptions.map((lang) => (
            <Button
              key={lang.code}
              variant="ghost"
              onClick={() => toggleLanguage(lang.code)}
              className={`block w-full text-left mb-1 ${language === lang.code ? "bg-accent" : ""}`}
            >
              {lang.name}
            </Button>
          ))}
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;
